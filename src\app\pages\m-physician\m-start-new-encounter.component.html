<div class="details-header">
    <button class="back-btn" (click)="back()">
        <img src="assets/icons/icon-caret-Left.svg" class="img-icon">
    </button>
    <div class="patient-name ">{{patient.patient_Name}} {{patient.sex}} ({{patient.age}}Y)</div>
</div>

<div class="w-full bg-white">
    <mat-tab-group (selectedTabChange)="onMatGroupTabClick($event)">
        <mat-tab label="Details">
            <ng-template matTabContent>
                <div class="w-full mobile-p-4">
                    <table class="table table-striped">
                        <tbody>
                            <tr>
                                <td class="text-secondary2">Account</td>
                                <td><span class="font-medium">{{patient.account_Number || '-'}}</span></td>
                            </tr>
                            <tr>
                                <td class="text-secondary2">Room</td>
                                <td><span class="font-medium">{{patient.room_Number || '-'}}</span></td>
                            </tr>
                            <tr>
                                <td class="text-secondary2">Type</td>
                                <td><span class="font-medium">{{patient.admission_Type || '-'}}</span></td>
                            </tr>
                            <tr>
                                <td class="text-secondary2">DOB</td>
                                <td><span class="font-medium">{{patient.dob || '-'}}</span></td>
                            </tr>
                            <tr>
                                <td class="text-secondary2">LOS</td>
                                <td><span class="font-medium">{{patient.arithmetic_Mean_LOS || '-'}}</span></td>
                            </tr>
                            <tr>
                                <td class="text-secondary2">Attending</td>
                                <td><span class="font-medium">{{patient.attending_Physician_InApp || '-'}}</span></td>
                            </tr>
                            <tr>
                                <td class="text-secondary2">Payor</td>
                                <td><span class="font-medium">{{patient.reimbursement_Type || '-'}}</span></td>
                            </tr>
                        </tbody>
                    </table>
                    <div class="w-full mt-4">
                        <ng-container *ngIf="encounterId; else apprvElse">
                            <button *ngIf="userAccess.residentAccess=='NO'; else btnAElse" class="mark-seen-btn"
                                (click)="approveAndEdit()">Approve</button>
                            <ng-template #btnAElse>
                                <button class="mark-seen-btn" (click)="updateChanges()">Update Changes</button>
                            </ng-template>
                            <button class="delete-encounter-btn" (click)="deleteEncounter()">Delete</button>
                        </ng-container>
                        <ng-template #apprvElse>
                            <button class="mark-seen-btn" (click)="viewSubmitEncounterPop()">
                                <span *ngIf="userAccess.residentAccess=='YES';else resEls">Submit for approval</span>
                                <ng-template #resEls>Mark As Seen</ng-template>
                            </button>
                        </ng-template>
                        <div class="details-buttons mt-2">
                            <button class="details-btn" (click)="viewPatientHistory()">
                                <img src="assets/icons/icon-history.svg" class="img-icon">
                            </button>
                            <button class="details-btn" (click)="viewNotes()">
                                <img src="assets/icons/icon-note.svg" class="img-icon">
                            </button>
                            <button class="details-btn" (click)="viewAttachments()">
                                <img src="assets/icons/icon-attachment.svg" class="img-icon">
                            </button>
                        </div>
                    </div>
                </div>
                <div class="code-content">
                    <!-- Subgroups starts -->
                    <div class="patient-card flex flex-col mobile-p-3"
                        *ngIf="testData.listofSubGroup?.length>0 && userAccess.residentAccess=='NO'">
                        <div class="flex flex-col justify-between mb-2">
                            <div class="px-2">
                                <div class="text-md font-medium">
                                    Subgroups
                                </div>
                                <div class="text-secondary text-sm">
                                    Defaults to {{testData.group_name}} group if no subgroup is selected
                                </div>
                            </div>
                        </div>
                        <div class="flex flex-col mt-3">
                            <div class="filter-tabs">
                                <div class="facility-dropdown mr-0">
                                    <mat-select id="subgroup" class="form-control" [(ngModel)]="sub_group_name"
                                        [ngModelOptions]="{standalone: true}">
                                        <mat-option [value]="''" disabled>---Select Subgroup---</mat-option>
                                        <mat-option [value]="s.subGroupName" *ngFor="let s of testData.listofSubGroup">
                                            {{s.subGroupName}}
                                        </mat-option>
                                    </mat-select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Subgroups ends -->
                    <!-- CPT Starts -->
                    <div class="patient-card flex flex-col mobile-p-3"
                        *ngIf="testData?.listofTestCPTS?.length>0; else noCPT">
                        <div class="flex justify-between mb-2">
                            <span class="font-medium px-2">CPT/HCPCS Codes</span>
                            <button class="close-btn" (click)="viewCPTCodes()">
                                <img src="assets/icons/icon-pluscircle-solid-s.svg" class="img-icon">
                            </button>
                        </div>
                        <div class="flex flex-col">
                            <div class="p-2 border-b" *ngFor="let cpt of testData.listofTestCPTS">
                                <div class="flex items-center">
                                    <button class="close-btn" (keyup)="deleteCPTCode(testData.listofTestCPTS,cpt)"
                                        (click)="deleteCPTCode(testData.listofTestCPTS,cpt)">
                                        <img src="assets/icons/icon-trash.svg" class="img-icon">
                                    </button>
                                    <div class="flex flex-col ml-2">
                                        <div>{{cpt}}</div>
                                        <div class="text-link text-sm" (click)="viewModifiers(cpt)">Add/Edit modifiers
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <ng-template #noCPT>
                        <div class="code-section">
                            <div class="code-item" (click)="viewCPTCodes()">
                                <div class="code-icon cpt-icon">
                                    <img src="assets/icons/icon-PlusCircle-s.svg" class="img-icon">
                                </div>
                                <div class="code-desc">
                                    <span class="font-medium">Add new CPT codes</span>
                                    <span class="chevron">
                                        <img src="assets/icons/icon-caret-Right.svg" class="img-icon">
                                    </span>
                                </div>
                            </div>
                        </div>
                    </ng-template>
                    <!-- CPT Ends -->
                    <!-- ICD Starts -->
                    <div class="patient-card flex flex-col mobile-p-3"
                        *ngIf="testData?.listofTestICDS?.length>0; else noICD">
                        <div class="flex justify-between mb-2">
                            <span class="font-medium px-2">ICD Codes</span>
                            <button class="close-btn" (click)="viewICDCodes()">
                                <img src="assets/icons/icon-pluscircle-solid-b.svg" class="img-icon">
                            </button>
                        </div>
                        <div class="flex flex-col">
                            <div class="p-2 border-b" *ngFor="let icd of testData.listofTestICDS">
                                <div class="flex items-center">
                                    <button class="close-btn" (keyup)="deleteICDCode(testData.listofTestICDS,icd)"
                                        (click)="deleteICDCode(testData.listofTestICDS,icd)">
                                        <img src="assets/icons/icon-trash.svg" class="img-icon">
                                    </button>
                                    <div class="flex flex-col ml-2">
                                        <div>{{icd}}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <ng-template #noICD>
                        <div class="code-section">
                            <div class="code-item" (click)="viewICDCodes()">
                                <div class="code-icon icd-icon">
                                    <img src="assets/icons/icon-PlusCircle-b.svg" class="img-icon">
                                </div>
                                <div class="code-desc">
                                    <span class="font-medium">Add new ICD codes</span>
                                    <span class="chevron">
                                        <img src="assets/icons/icon-caret-Right.svg" class="img-icon">
                                    </span>
                                </div>
                            </div>
                        </div>
                    </ng-template>
                    <!-- ICD Ends -->
                </div>
            </ng-template>
        </mat-tab>
        <mat-tab label="History">
            <ng-template matTabContent>
                <app-m-view-history [listOfPatientHistory]='listOfPatientHistory' [patient]='patient'
                    [historyTotalCount]="historyTotalCount"></app-m-view-history>
            </ng-template>
        </mat-tab>
        <mat-tab label="Note">
            <ng-template matTabContent>
                <app-m-send-note [PatientObject]='patient' [listOfUsersAndGroups]="listOfUsersAndGroups"
                    [lisfOfSentNotes]="lisfOfSentNotes" [selectedUsersAndGroups]="selectedUsersAndGroups"
                    (eventListOfNotes)="openNotes(patient)"></app-m-send-note>
            </ng-template>
        </mat-tab>
        <mat-tab label="Attachments">
            <ng-template matTabContent>
                <app-m-upload-attachment (eventUpdateCount)="updateAttCount(patient)"
                    [lisfOfAttachments]="lisfOfAttachments" [PatientObject]="patient"
                    [userType]="'PHYSICIAN'"></app-m-upload-attachment>
            </ng-template>
        </mat-tab>
    </mat-tab-group>
</div>

<!-- Submit Encounter Popup -->
<div *ngIf="showSubmitEncounterPopup">
    <div class="modal-backdrop"></div>
    <div class="actions-popup" [@slideLeft]>
        <div class="actions-header bg-white">
            <span>Submit Encounter</span>
            <button class="close-btn" (click)="closeSubmitEncounterPop()">
                <img src="assets/icons/icon-close.svg" class="img-icon">
            </button>
        </div>
        <div class="max-h-80 min-h-80 mobile-p-4 bg-white">
            <div class="attachment-list">
                <div class="w-full" *ngIf="userAccess.residentAccess=='YES'">
                    <mat-select id="physician" class="form-control" [(ngModel)]="selectedProvider"
                        [ngModelOptions]="{standalone: true}">
                        <mat-option [value]="'null'">Select Physician</mat-option>
                        <mat-option [value]="s.attending_provider"
                            *ngFor="let s of listOfProvider">{{s.attending_provider}}</mat-option>
                    </mat-select>
                </div>
                <ng-container *ngIf="facilityType=='0' && userAccess.residentAccess=='NO'; else singSel">
                    <div class="w-full">
                        <mat-slide-toggle [(ngModel)]="isMultipleEncounter"></mat-slide-toggle>
                    </div>
                    <div class="w-full" *ngIf="isMultipleEncounter; else calElse">
                        <mat-form-field [subscriptSizing]="'dynamic'" class="w-full">
                            <ngx-multiple-dates [matDatepicker]="picker" name="excludedDates"
                                [(ngModel)]="listOfEncounterSeenDates" [min]="minDate" [max]="maxDate"
                                placeholder="Select dates">
                            </ngx-multiple-dates>
                            <mat-datepicker-toggle matPrefix [for]="picker"></mat-datepicker-toggle>
                            <mat-datepicker #picker></mat-datepicker>
                        </mat-form-field>
                    </div>
                    <ng-template #calElse>
                        <div class="w-full">
                            <mat-form-field [subscriptSizing]="'dynamic'" class="w-full">
                                <mtx-datetimepicker #datetimePicker6 [type]="type" [mode]="mode"
                                    [multiYearSelector]="multiYearSelector" [startView]="startView"
                                    [twelvehour]="twelvehour" [timeInterval]="timeInterval" [touchUi]="touchUi"
                                    [timeInput]="timeInput">
                                </mtx-datetimepicker>
                                <input [mtxDatetimepicker]="datetimePicker6" [max]="maxDateTime" class="form-row"
                                    [(ngModel)]="testData.encounterSeenDate" matInput required>
                                <mtx-datetimepicker-toggle [for]="datetimePicker6"
                                    matSuffix></mtx-datetimepicker-toggle>
                            </mat-form-field>
                        </div>
                    </ng-template>
                </ng-container>
                <ng-template #singSel>
                    <div class="w-full">
                        <mat-form-field [subscriptSizing]="'dynamic'" class="w-full">
                            <mtx-datetimepicker #datetimePicker5 [type]="type" [mode]="mode"
                                [multiYearSelector]="multiYearSelector" [startView]="startView"
                                [twelvehour]="twelvehour" [timeInterval]="timeInterval" [touchUi]="touchUi"
                                [timeInput]="timeInput">
                            </mtx-datetimepicker>
                            <input [mtxDatetimepicker]="datetimePicker5" [max]="maxDateTime" class="form-row"
                                [(ngModel)]="testData.encounterSeenDate" matInput required>
                            <mtx-datetimepicker-toggle [for]="datetimePicker5" matSuffix></mtx-datetimepicker-toggle>
                        </mat-form-field>
                    </div>
                </ng-template>
            </div>
            <!-- Upload Section -->
            <div class="upload-section upload-footer">
                <ng-container *ngIf="facilityType=='0' && userAccess.residentAccess=='NO'; else singSel2">
                    <button class="upload-btn" (click)="selectMultiSelectDate()">Submit</button>
                </ng-container>
                <ng-template #singSel2>
                    <button class="upload-btn" (click)="starNewEncounter(0)">Submit</button>
                </ng-template>
            </div>
        </div>

    </div>
</div>

<!-- Confirm Message Modal Start -->
<div class="modal fade" id="markAsSeenConfModel" tabindex="-1" aria-labelledby="markAsSeenConfModelLabel"
    aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable modal-lg">
        <div class="modal-content">
            <div class="modal-header py-2">
                <h5 class="modal-title h5" id="markAsSeenConfModelLabel">Confirm Message</h5>
                <i class="fa fa-times closePopup" data-dismiss="modal"></i>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <span *ngIf="isMultipleEncounter">Selected encounter seen date: <span
                            class='font-weight-bold'>{{encounterSeenDates}}</span></span>
                    <span *ngIf="isMultipleEncounter">Same CPT and ICD codes will be applied for all the selected
                        encounters</span>
                    <div class="text-center"><span class="font-weight-bold">Are you sure want to proceed?</span></div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-secondary btn-sm" data-dismiss="modal">No</button>
                <button type="button" class="btn btn-outline-info btn-sm"
                    (click)="confirmStarNewEncounter(encounterStatus)">Yes</button>
            </div>
        </div>
    </div>
</div>
<!-- Confirm Message End -->

<!-- CPT Codes popup starts -->
<div *ngIf="showCPTCodesPopup">
    <div class="modal-backdrop"></div>
    <div class="actions-popup" [@slideLeft]>
        <div class="actions-header bg-white px-4 py-3">
            <div class="flex flex-col">
                <span>CPT Codes</span>
                <span class="text-secondary text-sm">Optional description</span>
            </div>
            <button class="close-btn" (click)="closeCPTCodesPopup()">
                <img src="assets/icons/icon-close.svg" class="img-icon">
            </button>
        </div>
        <div class="max-h-80 min-h-80 bg-white">
            <app-m-cptdata [lisfOfCPTData]="lisfOfCPTData"></app-m-cptdata>
        </div>
    </div>
</div>
<!-- CPT Codes popup ends -->

<!-- CPT Codes popup starts -->
<div *ngIf="showModifiersPopup">
    <div class="modal-backdrop"></div>
    <div class="actions-popup" [@slideLeft]>
        <div class="actions-header bg-white px-4 py-3">
            <div class="flex flex-col">
                <span>Assign Modifiers</span>
            </div>
            <button class="close-btn" (click)="closeModifiersPopup()">
                <img src="assets/icons/icon-close.svg" class="img-icon">
            </button>
        </div>
        <div class="max-h-80 min-h-80 bg-white">
            <app-m-cpt-modifiers [listOfModifier]="listOfModifier" [cptCode]="cptCode"></app-m-cpt-modifiers>
        </div>
    </div>
</div>
<!-- CPT Codes popup ends -->

<!-- ICD Codes popup starts -->
<div *ngIf="showICDCodesPopup">
    <div class="modal-backdrop"></div>
    <div class="actions-popup" [@slideLeft]>
        <div class="actions-header bg-white px-4 py-3">
            <div class="flex flex-col">
                <span>ICD Codes</span>
                <span class="text-secondary text-sm">Optional description</span>
            </div>
            <button class="close-btn" (click)="closeICDCodesPopup()">
                <img src="assets/icons/icon-close.svg" class="img-icon">
            </button>
        </div>
        <div class="max-h-80 min-h-80 bg-white">
            <app-m-icddata [lisfOfICDData]="lisfOfICDData"></app-m-icddata>
        </div>
    </div>
</div>
<!-- ICD Codes popup end -->

<!-- Patient History Popup -->
<div *ngIf="showHistoryPopup">
    <div class="modal-backdrop"></div>
    <div class="actions-popup" [@slideUp]>
        <div class="actions-header">
            <span>Past Encounters</span>
            <button class="close-btn" (click)="closePatientHistoryPopup()">
                <img src="assets/icons/icon-close.svg" class="img-icon">
            </button>
        </div>
        <div class="max-h-70">
            <app-m-view-history [listOfPatientHistory]='listOfPatientHistory' [patient]='patient'
                [historyTotalCount]="historyTotalCount"></app-m-view-history>
        </div>
    </div>
</div>

<!-- send note Popup Starts -->
<div *ngIf="showNotesPopup">
    <div class="modal-backdrop"></div>
    <div class="actions-popup" [@slideLeft]>
        <div class="actions-header bg-white">
            <span>Notes</span>
            <button class="close-btn" (click)="closeNotesPopup()">
                <img src="assets/icons/icon-close.svg" class="img-icon">
            </button>
        </div>
        <div class="max-h-80 min-h-80 bg-white">
            <app-m-send-note [PatientObject]='patient' [listOfUsersAndGroups]="listOfUsersAndGroups"
                [lisfOfSentNotes]="lisfOfSentNotes" [selectedUsersAndGroups]="selectedUsersAndGroups"
                (eventListOfNotes)="openNotes(patient)"></app-m-send-note>
        </div>
    </div>
</div>
<!-- send note Popup Ends -->

<!-- attachment popup starts -->
<div *ngIf="showAttachmentsPopup">
    <div class="modal-backdrop"></div>
    <div class="actions-popup" [@slideLeft]>
        <div class="actions-header bg-white">
            <span>Attachments</span>
            <button class="close-btn" (click)="closeAttachmentsPopup()">
                <img src="assets/icons/icon-close.svg" class="img-icon">
            </button>
        </div>
        <div class="max-h-80 min-h-80 bg-white">
            <app-m-upload-attachment (eventUpdateCount)="updateAttCount(patient)"
                [lisfOfAttachments]="lisfOfAttachments" [PatientObject]="patient"
                [userType]="'PHYSICIAN'"></app-m-upload-attachment>
        </div>
    </div>
</div>
<!-- attachment popup ends -->