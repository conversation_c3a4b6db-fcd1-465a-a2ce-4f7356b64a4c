<div class="attachment-list mobile-p-3">
    <div class="attachment-item" *ngFor="let item of lisfOfSentNotes">
        <div class="flex justify-between">
            <div class="flex">
                <div class="flex items-center">
                    <div
                        class="flex h-40px w-40px items-center justify-center overflow-hidden rounded-md bg-primary-100">
                        <img src="assets/icons/icon-24-white.svg" class="img-icon">
                    </div>
                    <div class="ml-2">
                        <div class="text-md font-medium">
                            {{item.fullName}}
                        </div>
                        <div class="text-secondary text-sm">
                            {{item.notescteateddate| date:'MMM d, y, h:mm:ss a'}}
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="flex mt-2">
            {{item.message}}
        </div>
    </div>
</div>
<!-- Upload Section -->
<div class="upload-section upload-footer" [formGroup]="noteForm">
    <div class="facility-dropdown mr-0">
        <label>Users and Groups</label>
        <ng-multiselect-dropdown id="inputAddress" [placeholder]="'--select user and group--'"
            [data]="listOfUsersAndGroups" [(ngModel)]="selectedUsersAndGroups" [ngModelOptions]="{standalone: true}"
            [settings]="mDdlUsersAndGroupsSettings" (onSelect)="onItemNoteSelect($event)">
        </ng-multiselect-dropdown>
        <div *ngIf="ddlVali" style="color: #e74a3b;">Users And Groups is required</div>
    </div>
    <input type="text" class="form-control" placeholder="Comment" formControlName="txtMessage"
        [ngClass]="{ 'is-invalid': submitted && f['txtMessage'].errors}">
    <div *ngIf="submitted && f['txtMessage'].errors" class="invalid-feedback">
        <br />
        <div *ngIf="f['txtMessage'].errors['required']">Message is required</div>
        <div *ngIf="f['txtMessage'].errors['maxlength']">Max length is 400 characters.</div>
    </div>
    <button class="upload-btn" type="submit" (click)="insertNote(PatientObject)">Send</button>
</div>