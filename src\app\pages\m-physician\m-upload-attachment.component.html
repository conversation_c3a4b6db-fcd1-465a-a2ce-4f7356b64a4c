<div class="attachment-list mobile-p-3">
    <div class="attachment-item" *ngFor="let item of lisfOfAttachments">
        <div class="flex justify-between">
            <div class="flex">
                <div class="flex items-center">
                    <div
                        class="flex h-50px w-50px items-center justify-center overflow-hidden rounded-md bg-primary-100">
                        <div class="flex items-center justify-center text-sm font-medium">
                            {{item.doC_NAME_TYPE}}
                        </div>
                    </div>
                    <div class="ml-2 text-break">
                        <div class="text-md font-medium">
                            {{item.doC_NAME}}
                        </div>
                        <div class="text-secondary text-sm font-medium">
                            {{item.createD_BY_NAME}}
                        </div>
                    </div>
                </div>
            </div>
            <div class="attachment-actions">
                <button class="action-btn" (click)="downloadAttachment(item.doC_NAME)">
                    <img src="assets/icons/icon-Download.svg" class="img-icon">
                </button>
                <button class="action-btn" *ngIf="item.isDelete && userType!='COMMON'"
                    (click)="deleteAttachment(lisfOfAttachments,item)">
                    <img src="assets/icons/icon-eye.svg" class="img-icon">
                </button>
            </div>
        </div>
        <div class="flex mt-2">
            {{item.comments}}
        </div>
    </div>
</div>
<!-- Upload Section -->
<div class="upload-section upload-footer">
    <label class="file-upload-label">
        <input #file type="file" id="attchFileId" (change)="uploadChange(file.files)" style="display:none" />
        <span class="file-upload-text">
            <img src="assets/icons/icon-PlusCircle-s.svg" class="upload-icon" />
            Click here to browse files
        </span>
    </label>
    <label *ngIf="fileName">{{fileName}}</label>
    <input type="text" class="form-control" placeholder="Comment Optional" [(ngModel)]="comment" maxlength="1000"
        [ngModelOptions]="{standalone: true}" />
    <div class="row mx-auto" *ngIf="submitted">
        <div class="col">
            <div class="text-danger" *ngIf="isFileRequired && submitted">
                Please select file.
            </div>
            <div class="text-danger" *ngIf="isCommentLength && submitted">
                Max length for comment is 400 characters.
            </div>
            <div class="text-danger" *ngIf="isFileValidName && submitted">
                Please upload a file with valid name.
            </div>
            <div class="text-danger" *ngIf="isFile && submitted">
                Please select a valid file to upload.
            </div>
            <div class="text-danger" *ngIf="isFileSize && submitted">
                Uploaded file size should be less than 20 MB.
            </div>
        </div>
    </div>
    <button class="upload-btn" (click)="uploadSubmit(PatientObject)">Upload</button>
</div>