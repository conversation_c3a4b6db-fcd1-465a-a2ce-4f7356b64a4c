<div class="patient-list-container">
    <!-- Header Section -->
    <div class="header-section">
        <!-- Search Bar -->
        <div class="search-container">
            <div class="search-input-wrapper">
                <i class="fas fa-search search-icon"></i>
                <input type="text" class="search-input" placeholder="Search for Name/Account#"
                       [(ngModel)]="searchKey" [ngModelOptions]="{standalone: true}"
                       (keyup)="search(searchKey)">
            </div>
            <button class="sort-btn" (click)="toggleSideNav('sort')">
                <img src="assets/icons/icon-sort.svg" class="img-icon">
            </button>
            <button class="filter-btn">
                <img src="assets/icons/icon-filter-active.svg" class="img-icon">
            </button>
        </div>

        <!-- Filter Tabs -->
        <div class="filter-tabs" [formGroup]="FilterForm">
            
            <div class="facility-dropdown">
                <mat-select id="ddlFacility" formControlName="ddlFacility" (selectionChange)="getUnBilledEncounters()"
                    class="form-control" [ngClass]="{ 'is-invalid': submitted && f['ddlFacility'].errors}">
                    <mat-option [value]="''" disabled>Select Facility</mat-option>
                    <mat-option [value]="'All'">All Facilities</mat-option>
                    <mat-option [value]="s.facilityName"
                        *ngFor="let s of listOfFacilities">{{s.facilityName}}</mat-option>
                </mat-select>
            </div>
        </div>

        <!-- Sort Panel -->

        <div class="filter-tabs mb-3" *ngIf="hideSideNav">
        <div class="facility-dropdown">
            <mat-label>Sort By</mat-label>
            <mat-select
            id="sortColumn"
            (selectionChange)="onChangeForSort($event.value)"
            [value]="sortColumnBy"
            class="form-control">
            <mat-option *ngFor="let sortOption of sortOptions" [value]="sortOption.id">
                {{ sortOption.name }}
            </mat-option>
            </mat-select>
        </div>

        <div class="facility-dropdown">
            <mat-label>Sort Order</mat-label>
            <mat-select
            id="sortOrder"
            (selectionChange)="onChangeForSortOrder($event.value)"
            [value]="orderBy"
            class="form-control">
            <mat-option *ngFor="let sortOrder of sortOrders" [value]="sortOrder">
                {{ sortOrder }}
            </mat-option>
            </mat-select>
        </div>
</div>

  
        
    </div>

    <!-- Patient Cards -->
    <div class="patient-cards">
        <ng-container *ngFor="let item of listOfPatients | paginate: {itemsPerPage:10, currentPage: p}; let i = index;">
            <div class="patient-card p-3 flex flex-col" *ngIf="item.account_Number!=''">
                <div class="flex flex-col p-6">
                    <div class="flex">
                        <span class="mobile-text-lg font-medium text-break">{{item.patient_Name}}</span>
                        <div class="mx-2 h-6 border-l-2"></div>
                        <span class="font-normal">{{item.facility_Name}}</span>
                    </div>
                    <div class="my-2 h-1 w-12 border-t-2"></div>
                    <div class="grid w-full grid-cols-2 gap-x-4">
                        <div><strong>Account:</strong> {{item.account_Number}}</div>
                        <div><strong>Missed Encounter Date:</strong> {{item.encounterseendate}}</div>
                        <div><strong>Admission Date:</strong> {{item.admit_Datetime | date: 'MM/dd/yyyy hh:mm:ss a'}}</div>
                        <div class="flex items-center">
                            <span *ngIf='item.encounterid ==0'>New Encounter</span>
                            <span *ngIf='item.encounterid !=0'>My Encounter</span>
                        </div>
                    </div>
                </div>
                <div class="action-buttons">
                    <button class="action-btn delete-btn" (click)="openDeleteConfirmPopup(item)" title="Remove Missing Encounter">
                        <img src="assets/icons/icon-trash-red.svg" class="img-icon px-6">
                    </button>
                    <button class="action-btn" *ngIf="residentAccess == 'YES'; else resEls"
                        [routerLink]="['/m/physician/approve-pending-encounter']"
                        [state]="{patient:item,backUrl:'/m/physician/unbilled-encounters'}">
                        <img src="assets/icons/icon-start.svg" class="img-icon px-6">
                    </button>
                    <ng-template #resEls>
                        <button class="action-btn" [routerLink]="['/m/physician/start-new-encounter']"
                            [state]="{patient:item,encounterSeenDate:item.encounterseendate,backUrl:'/m/physician/unbilled-encounters',facilityType:item.isPrimeFacility,filterObj:{p:p,searchByName:searchKey,ddlFacility:FilterForm.value.ddlFacility,ddlGroups:FilterForm.value.ddlGroups,ddlphybygroup:FilterForm.value.ddlphybygroup,ddlPatients:FilterForm.value.ddlPatients,ddlDepartment:FilterForm.value.ddlDepartment,txtFromDate:FilterForm.value.txtFromDate,txtToDate:FilterForm.value.txtToDate}}">
                            <img src="assets/icons/icon-start.svg" class="img-icon px-6">
                        </button>
                    </ng-template>
                </div>
            </div>
        </ng-container>

        <!-- No Data Message -->
        <div class="patient-card p-3 flex flex-col" *ngIf="listOfPatients.length === 0">
            <div class="flex flex-col p-6 items-center justify-center h-20">
                No data
            </div>
        </div>
        <!-- Pagination -->
        <div class="pagination-container" *ngIf="listOfPatients.length > 0">
            <pagination-controls previousLabel="" nextLabel="" (pageChange)="p = $event">
            </pagination-controls>
        </div>

    </div>

</div>

