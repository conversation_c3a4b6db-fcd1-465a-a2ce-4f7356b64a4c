<div class="w-full">
    <mat-tab-group (selectedTabChange)="onMatGroupTabClick($event)">
        <mat-tab label="Favorites">
            <ng-template matTabContent>
                <div class="p-3 mb-8">
                    <div class="search-container">
                        <div class="search-input-wrapper">
                            <i class="fas fa-search search-icon"></i>
                            <input type="text" class="search-input" placeholder="Search by keyword"
                             [(ngModel)]="searchCPTs" [ngModelOptions]="{standalone: true}">
                        </div>
                    </div>
                    <div class="flex flex-col">
                        <div class="flex" *ngFor="let item of lisfOfCPTData |gridFilter:{cptname:searchCPTs}:false">
                            <ng-container *ngIf="item.status">
                            <mat-checkbox [color]="'primary'" [checked]="item.isExist" (change)="chkChangeEvent($event)"
                             [id]="item.cpT_ID" value="{{item.cptname}}">
                                <span>{{item.cptname}}</span>
                            </mat-checkbox>
                            <div class="flex items-center ml-2">
                                <img src="assets/icons/icon-star-favorite.svg" class="img-icon" (click)="favUnfavCPTCodesAdd(false,item)">
                            </div>
                            </ng-container>
                        </div>
                    </div>
                </div>
                <div class="upload-section upload-footer">
                    <button class="add-btn" (click)="addCPTData(lisfOfCPTData)">Add</button>
                </div>
            </ng-template>
        </mat-tab>
        <mat-tab label="All">
            <ng-template matTabContent>
                <div class="p-3 mb-8">
                    <div class="search-container">
                        <div class="search-input-wrapper">
                            <i class="fas fa-search search-icon"></i>
                            <input type="text" class="search-input" placeholder="Search by keyword"
                             [(ngModel)]="filterCPTs" [ngModelOptions]="{standalone: true}" (keyup)="searchCPTData()">
                        </div>
                    </div>
                    <div class="flex flex-col">
                        <div class="flex" *ngFor="let item of lisfOfSearchCPTData">
                            <mat-checkbox [color]="'primary'" [checked]="item.isExist"
                            id="sch-{{item.cpT_ID}}" value="{{item.cptname}}" (change)="chkChangeEventSearch($event)">
                                <span>{{item.cptname}}</span>
                            </mat-checkbox>
                            <div class="flex items-center ml-2">
                                <img *ngIf="item.status" src="assets/icons/icon-star-favorite.svg" class="img-icon" (click)="favUnfavCPTCodesAdd(false,item)">
                                <img *ngIf="!item.status" src="assets/icons/icon-star-default.svg" class="img-icon" (click)="favUnfavCPTCodesAdd(true,item)">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="upload-section upload-footer">
                    <button class="add-btn" (click)="addCPTDataSearch(lisfOfSearchCPTData)">Add</button>
                </div>
            </ng-template>
        </mat-tab>
    </mat-tab-group>
</div>