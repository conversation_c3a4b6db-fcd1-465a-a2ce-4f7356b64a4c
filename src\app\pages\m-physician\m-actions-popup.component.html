<div class="actions-card">
    <div class="flex flex-col">
        <div class="action-item px-4 py-3 border-b" *ngIf="patient.hasPriorHistory =='1';else noHistory"
            (click)="viewPatientHistory(patient)">
            <div class="flex flex-col">
                View Past History
            </div>
            <div>
                <img src="assets/icons/icon-history.svg" class="action-icon">
            </div>
        </div>
        <ng-template #noHistory>
            <div class="action-item px-4 py-3 border-b">
                <div class="flex flex-col">
                    No Prior History
                </div>
                <div>
                    <img src="assets/icons/icon-history.svg" class="action-icon">
                </div>
            </div>
        </ng-template>
        <div class="action-item px-4 py-3 border-b" (click)="viewNotes(patient)">
            <div class="flex flex-col">
                Notes
            </div>
            <div>
                <img src="assets/icons/icon-note.svg" class="action-icon">
            </div>
        </div>
        <div class="action-item px-4 py-3 border-b" (click)="viewAttachments(patient)">
            <div class="flex flex-col">
                Attachments
            </div>
            <div>
                <img src="assets/icons/icon-attachment.svg" class="action-icon">
            </div>
        </div>
    </div>
</div>
<div class="actions-card mt-4">
    <div class="flex flex-col">
        <!-- Post To Biller Starts -->
        <div class="action-item px-4 py-3 border-b"
            *ngIf="patient.hasPreviousEncounter=='1' && patient.resetPriorEncounterStatus=='1' && patient.isPostToBiller=='0'"
            (click)="UpdatePosttoBiller(patient)">
            <div class="flex flex-col">
                Posted to Biller
            </div>
            <div>
                <img src="assets/icons/icon-reimbursement.svg" class="action-icon">
            </div>
        </div>
        <div class="action-item px-4 py-3 border-b"
            *ngIf="patient.hasPreviousEncounter=='1' && patient.resetPriorEncounterStatus=='1' && patient.isPostToBiller=='1'">
            <div class="flex flex-col">
                Already Post To Biller
            </div>
            <div>
                <img src="assets/icons/icon-reimbursement.svg" class="action-icon">
            </div>
        </div>
        <div class="action-item px-4 py-3 border-b"
            *ngIf="patient.hasPreviousEncounter=='1' && (patient.encounteR_ID==null || patient.encounteR_ID=='0') && (patient.resetPriorEncounterStatus==null || patient.resetPriorEncounterStatus=='0') && patient.isPostToBiller=='0'"
            (click)="UpdatePosttoBiller(patient)">
            <div class="flex flex-col">
                Posted to Biller
            </div>
            <div>
                <img src="assets/icons/icon-reimbursement.svg" class="action-icon">
            </div>
        </div>
        <div class="action-item px-4 py-3 border-b"
            *ngIf="patient.hasPreviousEncounter=='1' && (patient.encounteR_ID==null || patient.encounteR_ID=='0') && (patient.resetPriorEncounterStatus==null || patient.resetPriorEncounterStatus=='0') && patient.isPostToBiller=='1'">
            <div class="flex flex-col">
                Already Post To Biller
            </div>
            <div>
                <img src="assets/icons/icon-reimbursement.svg" class="action-icon">
            </div>
        </div>
        <div class="action-item px-4 py-3 border-b" *ngIf="patient.hasPreviousEncounter=='0'">
            <div class="flex flex-col">
                No Encounter to Post To Biller
            </div>
            <div>
                <img src="assets/icons/icon-reimbursement.svg" class="action-icon">
            </div>
        </div>
        <!-- Post To Biller Ends -->
        <ng-container *ngIf="patient.isPrimeFacility=='0'">
            <div class="action-item px-4 py-3 border-b" (click)="viewEditPatient(patient)">
                <div class="flex flex-col">
                    Edit Patient Details
                </div>
                <div>
                    <img src="assets/icons/icon-edit.svg" class="action-icon">
                </div>
            </div>
            <div class="action-item px-4 py-3" (click)="CloseHospitalizationStatus(patient)">
                <div class="flex flex-col">
                    Close Hospitalization
                </div>
                <div>
                    <img src="assets/icons/icon-close-circle-outline.svg" class="action-icon">
                </div>
            </div>
        </ng-container>

    </div>
</div>